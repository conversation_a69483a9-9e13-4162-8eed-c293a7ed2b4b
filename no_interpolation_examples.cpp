// 不使用插值的几种方案示例

// ============================================================================
// 方案一：使用 flat 限定符（已在 main.cpp 中实现）
// ============================================================================
/*
顶点着色器：
"#version 130\n"
"uniform mat4 MVP;\n"
"in vec3 vCol;\n"
"in vec2 vPos;\n"
"flat out vec3 color;\n"  // flat 关键字阻止插值
"void main()\n"
"{\n"
"    gl_Position = MVP * vec4(vPos, 0.0, 1.0);\n"
"    color = vCol;\n"
"}\n";

片段着色器：
"#version 130\n"
"flat in vec3 color;\n"   // 对应的 flat 输入
"out vec4 FragColor;\n"
"void main()\n"
"{\n"
"    FragColor = vec4(color, 1.0);\n"
"}\n";

效果：整个三角形将显示为单一颜色（通常是第一个顶点的颜色）
*/

// ============================================================================
// 方案二：在片段着色器中使用固定颜色
// ============================================================================
/*
片段着色器（忽略顶点颜色）：
"#version 130\n"
"out vec4 FragColor;\n"
"void main()\n"
"{\n"
"    FragColor = vec4(1.0, 0.0, 0.0, 1.0);  // 固定红色\n"
"}\n";

效果：整个三角形显示为固定的红色
*/

// ============================================================================
// 方案三：使用纹理坐标实现离散颜色
// ============================================================================

// 修改顶点数据，添加纹理坐标来标识顶点
static const struct
{
    float x, y;
    float r, g, b;
    float tex_id;  // 添加纹理ID来标识顶点
} vertices_with_id[3] =
{
    { -0.6f, -0.4f, 1.f, 0.f, 0.f, 0.0f },  // 顶点0
    {  0.6f, -0.4f, 0.f, 1.f, 0.f, 1.0f },  // 顶点1
    {   0.f,  0.6f, 0.f, 0.f, 1.f, 2.0f }   // 顶点2
};

/*
顶点着色器：
"#version 130\n"
"uniform mat4 MVP;\n"
"in vec3 vCol;\n"
"in vec2 vPos;\n"
"in float vTexId;\n"
"out vec3 color;\n"
"flat out float texId;\n"  // 使用 flat 传递顶点ID
"void main()\n"
"{\n"
"    gl_Position = MVP * vec4(vPos, 0.0, 1.0);\n"
"    color = vCol;\n"
"    texId = vTexId;\n"
"}\n";

片段着色器：
"#version 130\n"
"in vec3 color;\n"
"flat in float texId;\n"
"out vec4 FragColor;\n"
"void main()\n"
"{\n"
"    vec3 finalColor;\n"
"    if (texId < 0.5) {\n"
"        finalColor = vec3(1.0, 0.0, 0.0);  // 红色区域\n"
"    } else if (texId < 1.5) {\n"
"        finalColor = vec3(0.0, 1.0, 0.0);  // 绿色区域\n"
"    } else {\n"
"        finalColor = vec3(0.0, 0.0, 1.0);  // 蓝色区域\n"
"    }\n"
"    FragColor = vec4(finalColor, 1.0);\n"
"}\n";

效果：三角形被分成三个纯色区域，没有渐变
*/

// ============================================================================
// 方案四：使用几何着色器控制颜色分布
// ============================================================================
/*
几何着色器可以为每个图元（三角形）生成统一的颜色：

"#version 330\n"
"layout(triangles) in;\n"
"layout(triangle_strip, max_vertices = 3) out;\n"
"in vec3 color[];\n"
"flat out vec3 finalColor;\n"
"void main()\n"
"{\n"
"    // 使用第一个顶点的颜色作为整个三角形的颜色\n"
"    vec3 triangleColor = color[0];\n"
"    \n"
"    for(int i = 0; i < 3; i++) {\n"
"        gl_Position = gl_in[i].gl_Position;\n"
"        finalColor = triangleColor;\n"
"        EmitVertex();\n"
"    }\n"
"    EndPrimitive();\n"
"}\n";

效果：整个三角形使用第一个顶点的颜色
*/

// ============================================================================
// 方案五：使用多个单色三角形
// ============================================================================

// 将一个三角形分割成多个小三角形，每个小三角形使用单一颜色
static const struct
{
    float x, y;
    float r, g, b;
} single_color_triangles[9] =  // 3个小三角形，每个3个顶点
{
    // 红色三角形（左下区域）
    { -0.6f, -0.4f, 1.f, 0.f, 0.f },
    { -0.2f, -0.4f, 1.f, 0.f, 0.f },
    { -0.4f,  0.0f, 1.f, 0.f, 0.f },
    
    // 绿色三角形（右下区域）
    {  0.2f, -0.4f, 0.f, 1.f, 0.f },
    {  0.6f, -0.4f, 0.f, 1.f, 0.f },
    {  0.4f,  0.0f, 0.f, 1.f, 0.f },
    
    // 蓝色三角形（顶部区域）
    { -0.2f,  0.2f, 0.f, 0.f, 1.f },
    {  0.2f,  0.2f, 0.f, 0.f, 1.f },
    {   0.f,  0.6f, 0.f, 0.f, 1.f }
};

/*
使用方法：
glDrawArrays(GL_TRIANGLES, 0, 9);  // 绘制3个三角形

效果：显示3个不同颜色的独立三角形，没有颜色混合
*/
