<?xml version="1.0" encoding="UTF-8"?>
<protocol name="viewporter">

  <copyright>
    Copyright © 2013-2016 Collabora, Ltd.

    Permission is hereby granted, free of charge, to any person obtaining a
    copy of this software and associated documentation files (the "Software"),
    to deal in the Software without restriction, including without limitation
    the rights to use, copy, modify, merge, publish, distribute, sublicense,
    and/or sell copies of the Software, and to permit persons to whom the
    Software is furnished to do so, subject to the following conditions:

    The above copyright notice and this permission notice (including the next
    paragraph) shall be included in all copies or substantial portions of the
    Software.

    THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
    IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
    FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
    THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
    LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
    DEALINGS IN THE SOFTWARE.
  </copyright>

  <interface name="wp_viewporter" version="1">
    <description summary="surface cropping and scaling">
      The global interface exposing surface cropping and scaling
      capabilities is used to instantiate an interface extension for a
      wl_surface object. This extended interface will then allow
      cropping and scaling the surface contents, effectively
      disconnecting the direct relationship between the buffer and the
      surface size.
    </description>

    <request name="destroy" type="destructor">
      <description summary="unbind from the cropping and scaling interface">
	Informs the server that the client will not be using this
	protocol object anymore. This does not affect any other objects,
	wp_viewport objects included.
      </description>
    </request>

    <enum name="error">
      <entry name="viewport_exists" value="0"
             summary="the surface already has a viewport object associated"/>
    </enum>

    <request name="get_viewport">
      <description summary="extend surface interface for crop and scale">
	Instantiate an interface extension for the given wl_surface to
	crop and scale its content. If the given wl_surface already has
	a wp_viewport object associated, the viewport_exists
	protocol error is raised.
      </description>
      <arg name="id" type="new_id" interface="wp_viewport"
           summary="the new viewport interface id"/>
      <arg name="surface" type="object" interface="wl_surface"
           summary="the surface"/>
    </request>
  </interface>

  <interface name="wp_viewport" version="1">
    <description summary="crop and scale interface to a wl_surface">
      An additional interface to a wl_surface object, which allows the
      client to specify the cropping and scaling of the surface
      contents.

      This interface works with two concepts: the source rectangle (src_x,
      src_y, src_width, src_height), and the destination size (dst_width,
      dst_height). The contents of the source rectangle are scaled to the
      destination size, and content outside the source rectangle is ignored.
      This state is double-buffered, and is applied on the next
      wl_surface.commit.

      The two parts of crop and scale state are independent: the source
      rectangle, and the destination size. Initially both are unset, that
      is, no scaling is applied. The whole of the current wl_buffer is
      used as the source, and the surface size is as defined in
      wl_surface.attach.

      If the destination size is set, it causes the surface size to become
      dst_width, dst_height. The source (rectangle) is scaled to exactly
      this size. This overrides whatever the attached wl_buffer size is,
      unless the wl_buffer is NULL. If the wl_buffer is NULL, the surface
      has no content and therefore no size. Otherwise, the size is always
      at least 1x1 in surface local coordinates.

      If the source rectangle is set, it defines what area of the wl_buffer is
      taken as the source. If the source rectangle is set and the destination
      size is not set, then src_width and src_height must be integers, and the
      surface size becomes the source rectangle size. This results in cropping
      without scaling. If src_width or src_height are not integers and
      destination size is not set, the bad_size protocol error is raised when
      the surface state is applied.

      The coordinate transformations from buffer pixel coordinates up to
      the surface-local coordinates happen in the following order:
        1. buffer_transform (wl_surface.set_buffer_transform)
        2. buffer_scale (wl_surface.set_buffer_scale)
        3. crop and scale (wp_viewport.set*)
      This means, that the source rectangle coordinates of crop and scale
      are given in the coordinates after the buffer transform and scale,
      i.e. in the coordinates that would be the surface-local coordinates
      if the crop and scale was not applied.

      If src_x or src_y are negative, the bad_value protocol error is raised.
      Otherwise, if the source rectangle is partially or completely outside of
      the non-NULL wl_buffer, then the out_of_buffer protocol error is raised
      when the surface state is applied. A NULL wl_buffer does not raise the
      out_of_buffer error.

      If the wl_surface associated with the wp_viewport is destroyed,
      all wp_viewport requests except 'destroy' raise the protocol error
      no_surface.

      If the wp_viewport object is destroyed, the crop and scale
      state is removed from the wl_surface. The change will be applied
      on the next wl_surface.commit.
    </description>

    <request name="destroy" type="destructor">
      <description summary="remove scaling and cropping from the surface">
	The associated wl_surface's crop and scale state is removed.
	The change is applied on the next wl_surface.commit.
      </description>
    </request>

    <enum name="error">
      <entry name="bad_value" value="0"
	     summary="negative or zero values in width or height"/>
      <entry name="bad_size" value="1"
	     summary="destination size is not integer"/>
      <entry name="out_of_buffer" value="2"
	     summary="source rectangle extends outside of the content area"/>
      <entry name="no_surface" value="3"
	     summary="the wl_surface was destroyed"/>
    </enum>

    <request name="set_source">
      <description summary="set the source rectangle for cropping">
	Set the source rectangle of the associated wl_surface. See
	wp_viewport for the description, and relation to the wl_buffer
	size.

	If all of x, y, width and height are -1.0, the source rectangle is
	unset instead. Any other set of values where width or height are zero
	or negative, or x or y are negative, raise the bad_value protocol
	error.

	The crop and scale state is double-buffered state, and will be
	applied on the next wl_surface.commit.
      </description>
      <arg name="x" type="fixed" summary="source rectangle x"/>
      <arg name="y" type="fixed" summary="source rectangle y"/>
      <arg name="width" type="fixed" summary="source rectangle width"/>
      <arg name="height" type="fixed" summary="source rectangle height"/>
    </request>

    <request name="set_destination">
      <description summary="set the surface size for scaling">
	Set the destination size of the associated wl_surface. See
	wp_viewport for the description, and relation to the wl_buffer
	size.

	If width is -1 and height is -1, the destination size is unset
	instead. Any other pair of values for width and height that
	contains zero or negative values raises the bad_value protocol
	error.

	The crop and scale state is double-buffered state, and will be
	applied on the next wl_surface.commit.
      </description>
      <arg name="width" type="int" summary="surface width"/>
      <arg name="height" type="int" summary="surface height"/>
    </request>
  </interface>

</protocol>
